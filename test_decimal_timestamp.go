package main

import (
	"fmt"
	"log"

	"github.com/abbychau/mist/mist"
)

func main() {
	engine := mist.NewSQLEngine()

	// Test creating table with DECIMAL and TIMESTAMP types
	fmt.Println("Creating table with DECIMAL and TIMESTAMP types...")
	createSQL := `CREATE TABLE test_table (
		id INT AUTO_INCREMENT PRIMARY KEY,
		amount DECIMAL(10, 2) NOT NULL,
		rate DECIMAL(5, 4) DEFAULT 0.0500,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	result, err := engine.Execute(createSQL)
	if err != nil {
		log.Fatalf("Failed to create table: %v", err)
	}
	fmt.Printf("Result: %v\n", result)

	// Test inserting data
	fmt.Println("\nInserting data...")
	insertSQL := `INSERT INTO test_table (amount) VALUES (1234.56)`

	result, err = engine.Execute(insertSQL)
	if err != nil {
		log.Fatalf("Failed to insert data: %v", err)
	}
	fmt.Printf("Result: %v\n", result)

	// Test selecting the data
	fmt.Println("\nSelecting data...")
	selectResult, err := engine.Execute("SELECT * FROM test_table")
	if err != nil {
		log.Fatalf("Failed to select data: %v", err)
	}

	if sr, ok := selectResult.(*mist.SelectResult); ok {
		fmt.Printf("Columns: %v\n", sr.Columns)
		for i, row := range sr.Rows {
			fmt.Printf("Row %d: %v\n", i, row)
			for j, val := range row {
				fmt.Printf("  Column %d (%s): %v (type: %T)\n", j, sr.Columns[j], val, val)
			}
		}
	}
}
